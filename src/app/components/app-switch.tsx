"use client";

import noop from "lodash/noop";
import { useCallback } from "react";

import { cn } from "../helpers/cn";

type AppSwitchProps = {
  onChange?: (enabled: boolean) => void;
  className?: string;
  enabled?: boolean;
};

export const AppSwitch = ({
  enabled = false,
  onChange = noop,
  className = "",
}: AppSwitchProps) => {
  const onSwitchChange = useCallback(() => {
    onChange(!enabled);
  }, [enabled, onChange]);

  return (
    <label className={cn("flex items-center cursor-pointer", className)}>
      <div className="relative">
        <input
          type="checkbox"
          className="sr-only"
          checked={enabled}
          onChange={onSwitchChange}
        />
        <div
          className={cn(
            "block h-[20px] w-[36px] rounded-full shadow-inner transition-colors duration-200",
            enabled ? "bg-accent-primary" : "bg-back-neutral-tertiary"
          )}
        />
        <div
          className={cn(
            "dot absolute left-1 top-[3px] size-[14px] rounded-full bg-fore-on-accent-primary transition-all duration-200 shadow",
            {
              "translate-x-full border border-accent-primary": enabled,
              "translate-x-0 border border-stroke-neutral-decorative": !enabled,
            }
          )}
        />
      </div>
    </label>
  );
};
