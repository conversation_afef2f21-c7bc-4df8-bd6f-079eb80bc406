"use client";

import { FiChevronLeft, FiChevronRight } from "react-icons/fi";
import { H6 } from "../app-typography";
import VideoPlayer from "../VideoPlayer/VideoPlayer";
import { useState } from "react";

// Video data extracted from the original dashboard
export const DASHBOARD_VIDEOS = [
  {
    title: "Recon Euler EVC Demo",
    url: "https://www.youtube.com/embed/z0sktBuJfEI",
    duration: "7min",
    views: "53K views",
  },
  {
    title: "5 minutes tutorial",
    url: "https://www.youtube.com/embed/RwfiPrxbdBg",
    duration: "5min",
    views: "42K views",
  },
  {
    title: "15 minutes product tour",
    url: "https://www.youtube.com/embed/TvCm6MCHKs0?si=PC5k2P7Rt_UfLK6Q",
    duration: "15min",
    views: "38K views",
  },
  {
    title: "1 Hour workshop on writing better invariants",
    url: "https://www.youtube.com/embed/fXG2JwvoFZ0?si=P8Aa5qOIiwyh1avh",
    duration: "60min",
    views: "25K views",
  },
];

export const PRO_VIDEOS = [
  {
    title: "Run a job in the cloud",
    url: "https://www.youtube.com/embed/s4ci9zgIHiI",
    duration: "3min",
    views: "10K views",
  },
  {
    title: "Create and re-use recipes",
    url: "https://www.youtube.com/embed/bXT8Ye2EaGs",
    duration: "3min",
    views: "10K views",
  },
  {
    title: "Run jobs on PR and commit",
    url: "https://www.youtube.com/embed/Fnz4P5kxAD0",
    duration: "3min",
    views: "10K views",
  },
];

type AppVideo = {
  title: string;
  url: string;
  duration: string;
  views: string;
};

interface VideoCardProps {
  video: AppVideo;
  isActive?: boolean;
  onClick: () => void;
}

const VideoCard = ({ video, isActive = false, onClick }: VideoCardProps) => {
  return (
    <div
      className={`flex min-w-[300px] cursor-pointer flex-col rounded-xl border transition-all duration-200 ${
        isActive
          ? "bg-accent-primary/10 border-accent-primary"
          : "hover:border-accent-primary/50 border-fore-neutral-quaternary bg-back-neutral-secondary"
      }`}
      onClick={onClick}
    >
      <div className="relative h-40 overflow-hidden rounded-t-xl bg-back-neutral-tertiary">
        <div className="from-accent-primary/20 to-accent-secondary/20 absolute inset-0 bg-gradient-to-br" />

        <div className="absolute inset-0 flex items-center justify-center">
          <div className="flex size-14 items-center justify-center rounded-full bg-white/90 backdrop-blur-sm">
            <div className="flex size-10 items-center justify-center rounded-full bg-gradient-to-br from-accent-primary to-accent-secondary">
              <div className="ml-1 size-0 border-y-[6px] border-l-8 border-y-transparent border-l-white" />
            </div>
          </div>
        </div>
      </div>

      <div className="flex flex-col gap-2 p-4">
        <div className="flex flex-col gap-1">
          <H6
            className={`line-clamp-2 ${
              isActive ? "text-accent-primary" : "text-fore-neutral-primary"
            }`}
          >
            {video.title}
          </H6>
          <p className="text-sm text-fore-neutral-tertiary">
            {video.duration} • {video.views}
          </p>
        </div>
      </div>
    </div>
  );
};

export const AppVideoCarousel = ({ videos }: { videos: AppVideo[] }) => {
  const [selectedVideo, setSelectedVideo] = useState(videos[0]);

  const handlePrev = () => {
    const currentIndex = videos.findIndex(
      (video) => video.url === selectedVideo.url
    );
    const newIndex = currentIndex === 0 ? videos.length - 1 : currentIndex - 1;
    setSelectedVideo(videos[newIndex]);
  };

  const handleNext = () => {
    const currentIndex = videos.findIndex(
      (video) => video.url === selectedVideo.url
    );
    const newIndex = currentIndex === videos.length - 1 ? 0 : currentIndex + 1;
    setSelectedVideo(videos[newIndex]);
  };

  const handleVideoSelect = (video: AppVideo) => {
    setSelectedVideo(video);
  };

  return (
    <div className="flex flex-col gap-4">
      <div className="flex items-center justify-between">
        <H6 className="text-fore-neutral-primary">Tutorials Videos</H6>

        <div className="flex gap-4">
          <button
            onClick={handlePrev}
            className="flex size-8 items-center justify-center rounded-full border border-accent-primary bg-back-neutral-secondary p-1.5 text-fore-neutral-primary transition-all duration-200 hover:bg-accent-primary hover:text-white"
            aria-label="Previous video"
          >
            <FiChevronLeft size={16} />
          </button>
          <button
            onClick={handleNext}
            className="flex size-8 items-center justify-center rounded-full border border-accent-primary bg-back-neutral-secondary p-1.5 text-fore-neutral-primary transition-all duration-200 hover:bg-accent-primary hover:text-white"
            aria-label="Next video"
          >
            <FiChevronRight size={16} />
          </button>
        </div>
      </div>

      <div className="grid grid-cols-1 gap-6 lg:grid-cols-[2fr_1fr]">
        <div className="order-2 lg:order-1">
          <VideoPlayer
            link={selectedVideo?.url}
            overlayText={`Watch ${selectedVideo.title}`}
          />
        </div>

        <div className="order-1 lg:order-2">
          <div className="flex max-h-[500px] gap-4 overflow-scroll pb-2 lg:flex-col lg:pb-0">
            {videos.map((video) => (
              <VideoCard
                key={video.url}
                video={video}
                isActive={selectedVideo.url === video.url}
                onClick={() => handleVideoSelect(video)}
              />
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};
